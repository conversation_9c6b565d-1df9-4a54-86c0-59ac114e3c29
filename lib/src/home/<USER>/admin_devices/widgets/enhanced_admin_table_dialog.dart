import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/extensions/string_hardcoded.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/switch/custom_switch.dart';
import 'package:smart_team_web/src/widgets/text_widget/text_widget.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class EnhancedAdminTableDialog extends HookConsumerWidget {
  const EnhancedAdminTableDialog({
    required this.columns,
    required this.data,
    super.key,
  });

  final List<String> columns;
  final List<List<String>> data;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final rowsPerPage = useState(20);
    final selectedRows = useState<Set<int>>({});
    final selectAll = useState(false);

    final dataSource = useMemoized(
      () => DeviceDataSource(
        columns: columns,
        fullData: data,
        selectedRows: selectedRows.value,
        onSelectionToggled: (index, sel) {
          final s = {...selectedRows.value};
          sel ? s.add(index) : s.remove(index);
          selectedRows.value = s;
          selectAll.value = s.length == data.length && data.isNotEmpty;
        },
      ),
      [columns, data, selectedRows.value],
    );

    final pageCount =
        max(1, (data.length / rowsPerPage.value).ceil()).toDouble();

    return Column(
      children: [
        _Toolbar(
          selectAll: selectAll.value,
          toggleSelectAll: (v) {
            selectAll.value = v ?? false;
            selectedRows.value = selectAll.value
                ? {for (var i = 0; i < data.length; i++) i}
                : {};
            dataSource.setSelection(selectedRows.value);
          },
          onAction: (op) => _performOp(context, op, selectedRows.value),
        ),
        Expanded(
          child: SfDataGridTheme(
            data: SfDataGridThemeData(
              gridLineColor: AColor.black.withAlpha(80),
              headerColor: AColor.lightGrey.withAlpha(30),
            ),
            child: SfDataGrid(
              source: dataSource,
              rowsPerPage: rowsPerPage.value,
              allowColumnsResizing: true,
              allowFiltering: true,
              allowSorting: true,
              isScrollbarAlwaysShown: true,
              headerRowHeight: 44,
              rowHeight: 48,
              columns: [
                GridColumn(
                  columnName: 'checkbox',
                  width: 90,
                  label: Center(
                    child: Checkbox(
                      value: selectAll.value,
                      onChanged: (v) {
                        selectAll.value = v ?? false;
                        selectedRows.value = selectAll.value
                            ? {for (var i = 0; i < data.length; i++) i}
                            : {};
                        dataSource.setSelection(selectedRows.value);
                      },
                    ),
                  ),
                ),
                ...columns.map(
                  (c) => GridColumn(
                    columnName: c,
                    label: Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Text(
                        c,
                        overflow: TextOverflow.ellipsis,
                        style: ATextStyle.semiSmallRegular,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Container(
          height: 60,
          alignment: Alignment.center,
          child: SfDataPagerTheme(
            data: const SfDataPagerThemeData(
              selectedItemColor: AColor.primaryColor,
            ),
            child: SfDataPager(
              delegate: dataSource,
              pageCount: pageCount,
              availableRowsPerPage: const [10, 20, 30],
              onRowsPerPageChanged: (v) {
                if (v != null) rowsPerPage.value = v;
              },
            ),
          ),
        ),
      ],
    );
  }

  void _performOp(
      BuildContext context, String op, Set<int> selectedRowsGlobal) {
    if (selectedRowsGlobal.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(context.tr.validation.pleaseSelectAtLeastOneDevice)),
      );
      return;
    }
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$op: ${selectedRowsGlobal.length} ${context.tr.device}'),
      ),
    );
  }
}

class _Toolbar extends StatelessWidget {
  const _Toolbar({
    required this.selectAll,
    required this.toggleSelectAll,
    required this.onAction,
  });

  final bool selectAll;
  final ValueChanged<bool?> toggleSelectAll;
  final ValueChanged<String> onAction;

  @override
  Widget build(BuildContext context) {
    LoadingElevatedButton btn(
      String t, {
      Color bg = AColor.white,
      Color txt = AColor.textColor,
    }) =>
        LoadingElevatedButton(
          onPressed: () async => onAction(t),
          height: 40,
          text: t,
          backgroundColor: bg,
          textStyle: ATextStyle.small.copyWith(color: txt),
        );

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          Column(
            children: [
              TextWidget(context.tr.deactivateActivate,
                  style: ATextStyle.small),
              CustomSwitch(value: true, onChanged: (_) {}),
            ],
          ),
          const SizedBox(width: 8),
          btn(context.tr.common.reset),
          const SizedBox(width: 8),
          btn(context.tr.requestLocation),
          const SizedBox(width: 8),
          btn(context.tr.delete, bg: Colors.red, txt: Colors.white),
          const Spacer(),
          btn(context.tr.save, bg: AColor.primaryColor, txt: Colors.white),
        ],
      ),
    );
  }
}

class DeviceDataSource extends DataGridSource {
  DeviceDataSource({
    required List<String> columns,
    required List<List<String>> fullData,
    required Set<int> selectedRows,
    required this.onSelectionToggled,
  })  : _columns = columns,
        _full = fullData,
        _selected = {...selectedRows} {
    _buildRows();
  }

  final List<String> _columns;
  final List<List<String>> _full;
  final Set<int> _selected;
  final void Function(int, bool) onSelectionToggled;

  late List<DataGridRow> _rows;

  void setSelection(Set<int> newSel) {
    _selected
      ..clear()
      ..addAll(newSel);
    notifyListeners();
  }

  @override
  List<DataGridRow> get rows => _rows;

  @override
  int get rowCount => _full.length;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    final idx = _rows.indexOf(row);
    final globalIdx = idx;
    final cells = row.getCells();

    return DataGridRowAdapter(
      color: _selected.contains(globalIdx)
          ? AColor.lightGrey.withAlpha(20)
          : Colors.white,
      cells: [
        Center(
          child: Checkbox(
            value: _selected.contains(globalIdx),
            onChanged: (v) {
              if (v == null) return;
              v ? _selected.add(globalIdx) : _selected.remove(globalIdx);
              onSelectionToggled(globalIdx, v);
              notifyListeners();
            },
          ),
        ),
        ...cells.skip(1).map(
              (c) => Container(
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  '${c.value}',
                  style: ATextStyle.semiSmallRegular,
                ),
              ),
            ),
      ],
    );
  }

  void _buildRows() {
    _rows = _full.asMap().entries.map<DataGridRow>((e) {
      return DataGridRow(
        cells: [
          DataGridCell<bool>(
            columnName: 'checkbox',
            value: _selected.contains(e.key),
          ),
          ...e.value.asMap().entries.map(
                (c) => DataGridCell<String>(
                  columnName: _columns[c.key],
                  value: c.value,
                ),
              ),
        ],
      );
    }).toList();
  }
}
