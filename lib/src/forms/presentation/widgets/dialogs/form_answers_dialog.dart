part of '../../forms_view.dart';

class _FormAnswersDialog extends HookConsumerWidget {
  const _FormAnswersDialog({
    required this.formTemplate,
  });

  final FormTemplate formTemplate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formAnswers = ref.watch(formAnswersProvider(formTemplate.id!));

    List<List<RowDataModel<dynamic>>> getRowData(FormTemplateAnswers value) {
      return value.answers.indexed.map((answer) {
        return [
          RowDataModel<String>(
            columnName: context.tr.common.formSubmitter,
            value: answer.$2.submittedByUser,
          ),
          RowDataModel<DateTime>(
            columnName: context.tr.common.submissionDate,
            value: answer.$2.submittedAt,
            cellBuilder: () {
              return Text(
                DTUtil.dtToString(
                  answer.$2.submittedAt,
                  format: DTFormat.dayMonthYear,
                ),
                style: ATextStyle.text13,
              );
            },
          ),
          ...value.getRowData(ref),
        ];
      }).toList();
    }

    return BaseAsyncProviderWidget<FormTemplateAnswers>(
      value: formAnswers,
      builder: (value) {
        final pinnedColumns = [
          TableColumnModel(
            columnName: context.tr.common.formSubmitter,
            sortable: false,
          ),
          TableColumnModel(
            columnName: context.tr.common.submissionDate,
          ),
        ];
        final questionColumns = value.questions
            .map(
              (e) => TableColumnModel(
                columnName: e.title,
                filterable: e.type != FormFieldTypeEnum.file,
                sortable: e.type != FormFieldTypeEnum.file,
              ),
            )
            .toList();
        final allColumns = [
          ...pinnedColumns,
          ...questionColumns,
        ];

        return Padding(
          padding: const EdgeInsets.all(24),
          child: SmartTeamDataTable(
            columns: allColumns,
            stackedRowData: [
              StackedHeaderRow(
                cells: [
                  StackedHeaderCell(
                    columnNames:
                        pinnedColumns.map((e) => e.columnName).toList(),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 12),
                        child: Text(
                          context.tr.form.formRecordInfo,
                          style: ATextStyle.text16SemiBold,
                        ),
                      ),
                    ),
                  ),
                  StackedHeaderCell(
                    columnNames:
                        questionColumns.map((e) => e.columnName).toList(),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 12),
                        child: Text(
                          context.tr.form.formAnswers,
                          style: ATextStyle.text16SemiBold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
            rowData: getRowData(value),
          ),
        );
      },
    );
  }
}
