import 'package:flutter/material.dart';
import 'package:smart_team_common/smart_team_common.dart';
import 'package:smart_team_web/src/home/<USER>/widgets/hover_colored_icon.dart';
import 'package:smart_team_web/src/job_list/application/job_list_provider.dart';
import 'package:smart_team_web/src/job_list/enum/job_filter_enum.dart';
import 'package:smart_team_web/src/job_list/presentation/dialogs/add_new_job_widgets.dart';
import 'package:smart_team_web/src/shared/build_context/common_decoration.dart';
import 'package:smart_team_web/src/shared/build_context/screen_util_ext.dart';
import 'package:smart_team_web/src/shared/constants/app_text_style.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';
import 'package:smart_team_web/src/shared/utils/dt_format.dart';
import 'package:smart_team_web/src/shared/utils/dt_util.dart';
import 'package:smart_team_web/src/theme/colors.dart';
import 'package:smart_team_web/src/widgets/app_dialog/app_dialog.dart';
import 'package:smart_team_web/src/widgets/app_segmented_switcher.dart';
import 'package:smart_team_web/src/widgets/app_shimmer.dart';
import 'package:smart_team_web/src/widgets/base_async_provider_widget.dart';
import 'package:smart_team_web/src/widgets/button/loading_elevated_button.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_column_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/domain/table_row_model.dart';
import 'package:smart_team_web/src/widgets/kangoroo_data_table/st_data_table.dart';
import 'package:smart_team_web/src/widgets/popup/delete_entry_dialog.dart';
import 'package:smart_team_web/src/widgets/text_form_field/custom_text_form_field.dart';

part 'components/job_status_tabs.dart';
part 'components/job_tab_switcher.dart';
part 'components/job_table.dart';

@RoutePage(name: 'JobListRoute')
class JobListView extends HookConsumerWidget {
  const JobListView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final style = ref.watch(appStyleProvider);

    return Scaffold(
      backgroundColor: AColor.backgroundColor,
      body: DefaultTabController(
        length: 4,
        child: Padding(
          padding: context.responsive(
            desktop: const EdgeInsets.all(24),
            tablet: const EdgeInsets.all(16),
          ),
          child: Column(
            spacing: 16,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 16,
                children: [
                  const Spacer(),
                  const _JobTabSwitcher(),
                  const Spacer(),
                  LoadingElevatedButton(
                    height: 45,
                    onPressed: () async {
                      await AppDialog.show<void>(
                        context: context,
                        title: context.tr.common.addNewJob,
                        width: context.width * .95,
                        height: context.height * .95,
                        child: const AddNewJobWidgets(),
                      );
                    },
                    text: context.tr.common.addNewJob,
                  ),
                ],
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: CommonDecorations.containerDecoration(),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              child: Text(
                                context.tr.jobList,
                                style: style.text.title1,
                              ),
                            ),
                            const Spacer(),
                            Expanded(
                              flex: 5,
                              child: CustomTextFormField(
                                hintText: context.tr.searchNameTask,
                                debounceDuration: style.times.fast,
                                suffixIcon: const HoverColoredIcon(
                                  icon: Icons.search,
                                ),
                                onChanged: ref
                                    .read(jobListSearchQueryProvider.notifier)
                                    .setSearchQuery,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Builder(
                          builder: (context) {
                            final tabController =
                                DefaultTabController.of(context);
                            return TabBarView(
                              controller: tabController,
                              physics: const NeverScrollableScrollPhysics(),
                              children: const [
                                _JobTable(),
                                _JobTable(
                                  priorities: [3, 4],
                                ),
                                _JobTable(
                                  statuses: [TaskStatusEnum.completed],
                                ),
                                _JobTable(
                                  statuses: [
                                    TaskStatusEnum.pending,
                                    TaskStatusEnum.inProgress,
                                    TaskStatusEnum.waitingForInfo,
                                  ],
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
