import 'package:flutter/material.dart';
import 'package:smart_team_web/src/shared/extensions/context_extensions.dart';

enum JobFilter {
  all,
  important,
  completed,
  pending,
}

extension JobFilterX on JobFilter {
  String label(BuildContext ctx) {
    switch (this) {
      case JobFilter.all:
        return ctx.tr.allJobs;
      case JobFilter.important:
        return ctx.tr.importantJobs;
      case JobFilter.completed:
        return ctx.tr.completedJobs;
      case JobFilter.pending:
        return ctx.tr.unfinishedJobs;
    }
  }
}
